class ChangeAssignedUserIdToString < ActiveRecord::Migration[8.0]
  def up
    # Change assigned_user_id from string to integer (to match user id type)
    change_column :cases, :assigned_user_id, :integer, using: 'assigned_user_id::integer'
  end

  def down
    # Change back to uuid - generate proper UUIDs
    # First set all to null, then change type, then generate UUIDs
    execute "UPDATE cases SET assigned_user_id = NULL"
    change_column :cases, :assigned_user_id, :uuid
    # Generate simple UUIDs based on id
    execute <<-SQL
      UPDATE cases
      SET assigned_user_id = ('00000000-0000-0000-0000-' || LPAD(id::text, 12, '0'))::uuid
    SQL
  end
end
