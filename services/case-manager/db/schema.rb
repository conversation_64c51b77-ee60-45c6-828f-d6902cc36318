# This file is auto-generated from the current state of the database. Instead
# of editing this file, please use the migrations feature of Active Record to
# incrementally modify your database, and then regenerate this schema definition.
#
# This file is the source Rails uses to define your schema when running `bin/rails
# db:schema:load`. When creating a new database, `bin/rails db:schema:load` tends to
# be faster and is potentially less error prone than running all of your
# migrations from scratch. Old migrations may fail to apply correctly if those
# migrations use external dependencies or application code.
#
# It's strongly recommended that you check this file into your version control system.

ActiveRecord::Schema[8.0].define(version: 2025_07_27_205408) do
  # These are extensions that must be enabled in order to support this database
  enable_extension "pg_catalog.plpgsql"

  create_table "case_documents", force: :cascade do |t|
    t.uuid "case_id", null: false
    t.uuid "uploaded_by_id", null: false
    t.string "file_name", null: false
    t.string "file_type", null: false
    t.bigint "file_size", null: false
    t.string "document_type", null: false
    t.text "description"
    t.json "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["case_id"], name: "index_case_documents_on_case_id"
    t.index ["created_at"], name: "index_case_documents_on_created_at"
    t.index ["document_type"], name: "index_case_documents_on_document_type"
    t.index ["file_type"], name: "index_case_documents_on_file_type"
    t.index ["uploaded_by_id"], name: "index_case_documents_on_uploaded_by_id"
  end

  create_table "case_types", force: :cascade do |t|
    t.string "name", null: false
    t.string "name_ar", null: false
    t.integer "category", null: false
    t.boolean "active", default: true, null: false
    t.integer "display_order", default: 0
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active", "display_order", "name"], name: "index_case_types_on_active_and_display_order_and_name"
    t.index ["category"], name: "index_case_types_on_category"
    t.index ["project_id", "name"], name: "index_case_types_on_project_id_and_name", unique: true
  end

  create_table "cases", force: :cascade do |t|
    t.string "assigned_user_id"
    t.string "status"
    t.string "approval_status"
    t.datetime "started_at"
    t.datetime "closed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "project_id"
    t.string "case_number"
    t.string "case_type", default: "general"
    t.integer "priority_level", default: 1
    t.integer "confidentiality_level", default: 0
    t.json "beneficiary_cache", default: {}
    t.uuid "created_by_id"
    t.text "notes"
    t.string "beneficiary_name"
    t.integer "beneficiary_age"
    t.string "beneficiary_gender"
    t.string "beneficiary_nationality"
    t.string "beneficiary_phone"
    t.string "beneficiary_id_number"
    t.date "beneficiary_date_of_birth"
    t.string "beneficiary_city"
    t.index ["beneficiary_age"], name: "index_cases_on_beneficiary_age"
    t.index ["beneficiary_gender"], name: "index_cases_on_beneficiary_gender"
    t.index ["beneficiary_name"], name: "index_cases_on_beneficiary_name"
    t.index ["beneficiary_nationality"], name: "index_cases_on_beneficiary_nationality"
    t.index ["case_number"], name: "index_cases_on_case_number", unique: true
    t.index ["case_type"], name: "index_cases_on_case_type"
    t.index ["confidentiality_level"], name: "index_cases_on_confidentiality_level"
    t.index ["created_by_id"], name: "index_cases_on_created_by_id"
    t.index ["priority_level"], name: "index_cases_on_priority_level"
  end

  create_table "comments", force: :cascade do |t|
    t.uuid "case_id", null: false
    t.uuid "user_id", null: false
    t.string "commentable_type", null: false
    t.bigint "commentable_id", null: false
    t.bigint "parent_comment_id"
    t.text "content", null: false
    t.integer "comment_type", default: 0
    t.integer "status", default: 0
    t.datetime "resolved_at"
    t.uuid "resolved_by_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["case_id"], name: "index_comments_on_case_id"
    t.index ["comment_type"], name: "index_comments_on_comment_type"
    t.index ["commentable_type", "commentable_id"], name: "index_comments_on_commentable"
    t.index ["commentable_type", "commentable_id"], name: "index_comments_on_commentable_type_and_commentable_id"
    t.index ["parent_comment_id"], name: "index_comments_on_parent_comment_id"
    t.index ["resolved_at"], name: "index_comments_on_resolved_at"
    t.index ["resolved_by_id"], name: "index_comments_on_resolved_by_id"
    t.index ["status"], name: "index_comments_on_status"
    t.index ["user_id"], name: "index_comments_on_user_id"
  end

  create_table "field_type_definitions", force: :cascade do |t|
    t.string "name", null: false
    t.string "input_type", null: false
    t.integer "data_type", default: 0, null: false
    t.text "description"
    t.json "validation_schema"
    t.json "default_config"
    t.boolean "active", default: true
    t.integer "version", default: 1
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active", "name"], name: "index_field_type_definitions_on_active_and_name"
    t.index ["active"], name: "index_field_type_definitions_on_active"
    t.index ["data_type"], name: "index_field_type_definitions_on_data_type"
    t.index ["input_type"], name: "index_field_type_definitions_on_input_type"
    t.index ["name"], name: "index_field_type_definitions_on_name", unique: true
  end

  create_table "form_field_options", force: :cascade do |t|
    t.bigint "form_field_id", null: false
    t.string "option_key", null: false
    t.string "option_value", null: false
    t.integer "display_order", null: false
    t.boolean "active", default: true
    t.json "metadata", default: {}
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_form_field_options_on_active"
    t.index ["display_order"], name: "index_form_field_options_on_display_order"
    t.index ["form_field_id", "option_key"], name: "index_form_field_options_on_form_field_id_and_option_key", unique: true
    t.index ["form_field_id"], name: "index_form_field_options_on_form_field_id"
  end

  create_table "form_field_validations", force: :cascade do |t|
    t.bigint "form_field_id", null: false
    t.string "validation_type", null: false
    t.string "validation_value", null: false
    t.string "error_message", null: false
    t.boolean "active", default: true
    t.integer "priority", default: 0
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_form_field_validations_on_active"
    t.index ["form_field_id"], name: "index_form_field_validations_on_form_field_id"
    t.index ["priority"], name: "index_form_field_validations_on_priority"
    t.index ["validation_type"], name: "index_form_field_validations_on_validation_type"
  end

  create_table "form_fields", force: :cascade do |t|
    t.bigint "form_section_id", null: false
    t.string "field_name", null: false
    t.string "label", null: false
    t.string "field_type", null: false
    t.string "data_type", default: "string"
    t.integer "display_order", null: false
    t.boolean "required", default: false
    t.boolean "visible", default: true
    t.text "help_text"
    t.text "placeholder"
    t.json "validation_rules", default: {}
    t.json "field_config", default: {}
    t.string "calculation_formula"
    t.string "lookup_source_type"
    t.text "lookup_source_config"
    t.bigint "parent_field_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.json "depends_on"
    t.boolean "auto_calculate", default: false
    t.index ["auto_calculate"], name: "index_form_fields_on_auto_calculate"
    t.index ["field_name"], name: "index_form_fields_on_field_name"
    t.index ["field_type"], name: "index_form_fields_on_field_type"
    t.index ["form_section_id", "display_order"], name: "index_form_fields_on_form_section_id_and_display_order", unique: true
    t.index ["form_section_id"], name: "index_form_fields_on_form_section_id"
    t.index ["parent_field_id"], name: "index_form_fields_on_parent_field_id"
    t.index ["required"], name: "index_form_fields_on_required"
  end

  create_table "form_sections", force: :cascade do |t|
    t.bigint "form_template_id", null: false
    t.string "name", null: false
    t.string "title", null: false
    t.text "description"
    t.integer "display_order", null: false
    t.boolean "is_required", default: false
    t.boolean "visible", default: true
    t.bigint "display_condition_field_id"
    t.string "display_condition_value"
    t.string "display_condition_user_role"
    t.string "display_condition_project_type"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["display_condition_field_id"], name: "index_form_sections_on_display_condition_field_id"
    t.index ["form_template_id", "display_order"], name: "index_form_sections_on_form_template_id_and_display_order", unique: true
    t.index ["form_template_id"], name: "index_form_sections_on_form_template_id"
    t.index ["name"], name: "index_form_sections_on_name"
  end

  create_table "form_submissions", force: :cascade do |t|
    t.uuid "case_id", null: false
    t.bigint "form_template_id", null: false
    t.uuid "created_by_id", null: false
    t.uuid "updated_by_id"
    t.integer "status", default: 0
    t.json "form_data", default: {}
    t.json "completion_status", default: {}
    t.datetime "submitted_at"
    t.datetime "completed_at"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["case_id", "form_template_id"], name: "index_form_submissions_on_case_id_and_form_template_id", unique: true
    t.index ["case_id"], name: "index_form_submissions_on_case_id"
    t.index ["completed_at"], name: "index_form_submissions_on_completed_at"
    t.index ["created_by_id"], name: "index_form_submissions_on_created_by_id"
    t.index ["form_template_id"], name: "index_form_submissions_on_form_template_id"
    t.index ["status"], name: "index_form_submissions_on_status"
    t.index ["submitted_at"], name: "index_form_submissions_on_submitted_at"
  end

  create_table "form_templates", force: :cascade do |t|
    t.string "name", null: false
    t.string "title", null: false
    t.text "description"
    t.string "target_role", default: "both"
    t.integer "sequence_order", default: 0
    t.boolean "active", default: true
    t.json "prerequisite_forms", default: []
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active"], name: "index_form_templates_on_active"
    t.index ["name"], name: "index_form_templates_on_name", unique: true
    t.index ["project_id"], name: "index_form_templates_on_project_id"
    t.index ["target_role"], name: "index_form_templates_on_target_role"
  end

  create_table "geographic_locations", force: :cascade do |t|
    t.string "name", null: false
    t.string "name_ar", null: false
    t.integer "location_type", null: false
    t.string "iso_code"
    t.bigint "parent_location_id"
    t.boolean "active", default: true, null: false
    t.integer "display_order", default: 0
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active", "display_order", "name"], name: "idx_on_active_display_order_name_6c225d5227"
    t.index ["location_type"], name: "index_geographic_locations_on_location_type"
    t.index ["parent_location_id"], name: "index_geographic_locations_on_parent_location_id"
    t.index ["project_id", "name"], name: "index_geographic_locations_on_project_id_and_name", unique: true
  end

  create_table "partner_agencies", force: :cascade do |t|
    t.string "name", null: false
    t.string "name_ar", null: false
    t.integer "agency_type", null: false
    t.string "contact_email"
    t.boolean "active", default: true, null: false
    t.integer "display_order", default: 0
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active", "display_order", "name"], name: "index_partner_agencies_on_active_and_display_order_and_name"
    t.index ["agency_type"], name: "index_partner_agencies_on_agency_type"
    t.index ["project_id", "name"], name: "index_partner_agencies_on_project_id_and_name", unique: true
  end

  create_table "referral_sources", force: :cascade do |t|
    t.string "name", null: false
    t.string "name_ar", null: false
    t.integer "source_type", null: false
    t.boolean "active", default: true, null: false
    t.integer "display_order", default: 0
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active", "display_order", "name"], name: "index_referral_sources_on_active_and_display_order_and_name"
    t.index ["project_id", "name"], name: "index_referral_sources_on_project_id_and_name", unique: true
    t.index ["source_type"], name: "index_referral_sources_on_source_type"
  end

  create_table "service_categories", force: :cascade do |t|
    t.string "name", null: false
    t.string "name_ar", null: false
    t.integer "sector", null: false
    t.integer "target_group", null: false
    t.boolean "active", default: true, null: false
    t.integer "display_order", default: 0
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active", "display_order", "name"], name: "index_service_categories_on_active_and_display_order_and_name"
    t.index ["project_id", "name"], name: "index_service_categories_on_project_id_and_name", unique: true
    t.index ["sector"], name: "index_service_categories_on_sector"
    t.index ["target_group"], name: "index_service_categories_on_target_group"
  end

  create_table "services", force: :cascade do |t|
    t.string "name", null: false
    t.text "description", null: false
    t.string "category", null: false
    t.string "provider_type", null: false
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.integer "project_id"
  end

  create_table "staff_positions", force: :cascade do |t|
    t.string "name", null: false
    t.string "name_ar", null: false
    t.string "code", null: false
    t.string "department", null: false
    t.boolean "active", default: true, null: false
    t.integer "display_order", default: 0
    t.integer "project_id"
    t.datetime "created_at", null: false
    t.datetime "updated_at", null: false
    t.index ["active", "display_order", "name"], name: "index_staff_positions_on_active_and_display_order_and_name"
    t.index ["department"], name: "index_staff_positions_on_department"
    t.index ["project_id", "code"], name: "index_staff_positions_on_project_id_and_code", unique: true
  end

  add_foreign_key "comments", "comments", column: "parent_comment_id", on_delete: :cascade
  add_foreign_key "form_field_options", "form_fields"
  add_foreign_key "form_field_validations", "form_fields"
  add_foreign_key "form_fields", "form_fields", column: "parent_field_id", on_delete: :nullify
  add_foreign_key "form_fields", "form_sections"
  add_foreign_key "form_sections", "form_fields", column: "display_condition_field_id", on_delete: :nullify
  add_foreign_key "form_sections", "form_templates"
  add_foreign_key "form_submissions", "form_templates"
  add_foreign_key "geographic_locations", "geographic_locations", column: "parent_location_id"
end
